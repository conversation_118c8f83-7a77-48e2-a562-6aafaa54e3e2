import requests
import os

VAULT_URL = os.getenv("VAULT_URL", "http://vault:8080")

def fetch_and_decrypt(doc_id: str, out_path: str) -> str:
    url = f"{VAULT_URL}/decrypt/{doc_id}"
    r = requests.get(url)
    if r.status_code == 200:
        with open(out_path, "wb") as f:
            f.write(r.content)
        return out_path
    else:
        raise RuntimeError(f"Vault returned {r.status_code}: {r.text}")
